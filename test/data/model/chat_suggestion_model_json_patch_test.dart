import 'package:flutter_test/flutter_test.dart';
import 'package:family_app/data/model/chat_suggestion_model.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';

void main() {
  group('ChatSuggestionModel JSON Patch RFC 6902 Tests', () {
    // Standard trip JSON for testing
    final standardTripJson = {
      "uuid": "trip-123",
      "name": "Paris Adventure",
      "city": "Paris",
      "country": "France",
      "from_date": "2025-01-15T00:00:00.000Z",
      "to_date": "2025-01-20T23:59:59.999Z",
      "itinerary": [
        {
          "day": 1,
          "date": "2025-01-15",
          "activities": [
            {
              "time": "09:00",
              "activity": "Visit Eiffel Tower",
              "location": "Champ de Mars, Paris"
            },
            {
              "time": "14:00",
              "activity": "Louvre Museum",
              "location": "Rue de Rivoli, Paris"
            }
          ]
        },
        {
          "day": 2,
          "date": "2025-01-16",
          "activities": [
            {
              "time": "10:00",
              "activity": "Notre-Dame Cathedral",
              "location": "6 Parvis Notre-Dame, Paris"
            }
          ]
        }
      ],
      "budget": 1500.0,
      "travelers": 2
    };

    // Mock trip object with to<PERSON>son method
    MockTrip createStandardTrip() {
      return MockTrip(standardTripJson);
    }

    test('should handle ADD operation - add new day to itinerary', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Added a new day to your itinerary!",
  "patch": [
    {
      "op": "add",
      "path": "/itinerary/-",
      "value": {
        "day": 3,
        "date": "2025-01-17",
        "activities": [
          {
            "time": "11:00",
            "activity": "Versailles Palace",
            "location": "Place d'Armes, Versailles"
          }
        ]
      }
    }
  ]
}
```
''',
      };

      final currentTrip = createStandardTrip();
      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: currentTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message, equals('Added a new day to your itinerary!'));

      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['itinerary'], hasLength(3));
      expect(modifiedTrip['itinerary'][2]['day'], equals(3));
      expect(modifiedTrip['itinerary'][2]['activities'][0]['activity'],
          equals('Versailles Palace'));
    });

    test('should handle REPLACE operation - change trip end date', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Extended your trip by one day!",
  "patch": [
    {
      "op": "replace",
      "path": "/to_date",
      "value": "2025-01-21T23:59:59.999Z"
    }
  ]
}
```
''',
      };

      final currentTrip = createStandardTrip();
      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: currentTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message, equals('Extended your trip by one day!'));

      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['to_date'], equals('2025-01-21T23:59:59.999Z'));
      // Ensure other fields remain unchanged
      expect(modifiedTrip['from_date'], equals('2025-01-15T00:00:00.000Z'));
      expect(modifiedTrip['name'], equals('Paris Adventure'));
    });

    test('should handle REMOVE operation - remove a day from itinerary', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Removed day 2 from your itinerary.",
  "patch": [
    {
      "op": "remove",
      "path": "/itinerary/1"
    }
  ]
}
```
''',
      };

      final currentTrip = createStandardTrip();
      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: currentTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message, equals('Removed day 2 from your itinerary.'));

      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['itinerary'], hasLength(1));
      expect(modifiedTrip['itinerary'][0]['day'], equals(1));
      expect(modifiedTrip['itinerary'][0]['activities'][0]['activity'],
          equals('Visit Eiffel Tower'));
    });

    test('should handle MOVE operation - reorder itinerary days', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Reordered your itinerary days.",
  "patch": [
    {
      "op": "move",
      "from": "/itinerary/1",
      "path": "/itinerary/0"
    }
  ]
}
```
''',
      };

      final currentTrip = createStandardTrip();
      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: currentTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message, equals('Reordered your itinerary days.'));

      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['itinerary'], hasLength(2));
      // Day 2 should now be at index 0
      expect(modifiedTrip['itinerary'][0]['day'], equals(2));
      expect(modifiedTrip['itinerary'][0]['activities'][0]['activity'],
          equals('Notre-Dame Cathedral'));
      // Original day 1 should now be at index 1
      expect(modifiedTrip['itinerary'][1]['day'], equals(1));
    });

    test('should handle multiple operations in single patch', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Updated trip name and budget, and added new activity.",
  "patch": [
    {
      "op": "replace",
      "path": "/name",
      "value": "Romantic Paris Getaway"
    },
    {
      "op": "replace", 
      "path": "/budget",
      "value": 2000.0
    },
    {
      "op": "add",
      "path": "/itinerary/0/activities/-",
      "value": {
        "time": "19:00",
        "activity": "Seine River Cruise",
        "location": "Port de la Bourdonnais, Paris"
      }
    }
  ]
}
```
''',
      };

      final currentTrip = createStandardTrip();
      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: currentTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message,
          equals('Updated trip name and budget, and added new activity.'));

      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['name'], equals('Romantic Paris Getaway'));
      expect(modifiedTrip['budget'], equals(2000.0));
      expect(modifiedTrip['itinerary'][0]['activities'], hasLength(3));
      expect(modifiedTrip['itinerary'][0]['activities'][2]['activity'],
          equals('Seine River Cruise'));
    });

    test('should handle EDITING_COMPLETE status', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Great! Your trip plan is all set.",
  "patch": [],
  "status": "EDITING_COMPLETE"
}
```
''',
      };

      final currentTrip = createStandardTrip();
      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: currentTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message, equals('Great! Your trip plan is all set.'));
      expect(model.editingComplete, isTrue);

      // Trip should remain unchanged when editing is complete with empty patch
      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['name'], equals('Paris Adventure'));
      expect(modifiedTrip['itinerary'], hasLength(2));
    });

    test('should handle empty patch array gracefully', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "No changes needed.",
  "patch": []
}
```
''',
      };

      final currentTrip = createStandardTrip();
      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: currentTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message, equals('No changes needed.'));
      expect(model.editingComplete, isFalse);

      // Trip should remain unchanged
      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['name'], equals('Paris Adventure'));
      expect(modifiedTrip['budget'], equals(1500.0));
    });

    test('should handle malformed patch operations gracefully', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Attempted to update trip.",
  "patch": [
    {
      "op": "invalid_operation",
      "path": "/name",
      "value": "New Name"
    },
    {
      "op": "replace",
      "path": "/budget",
      "value": 2500.0
    }
  ]
}
```
''',
      };

      final currentTrip = createStandardTrip();
      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: currentTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message, equals('Attempted to update trip.'));

      // Should apply valid operations and ignore invalid ones
      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['budget'], equals(2500.0));
      // Name should remain unchanged due to invalid operation
      expect(modifiedTrip['name'], equals('Paris Adventure'));
    });
  });
}
