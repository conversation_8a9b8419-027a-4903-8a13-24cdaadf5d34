import 'dart:convert';

class ChatSuggestionModel {
  final String message;
  final List<String> suggestions;
  final Map<String, dynamic>? aiTrip;
  final Map<String, dynamic>? aiEvent;
  final Map<String, dynamic>? aiList;
  final Map<String, dynamic>? aiTripIntent;

  ChatSuggestionModel({
    required this.message,
    required this.suggestions,
    this.aiTrip,
    this.aiEvent,
    this.aiList,
    this.aiTripIntent,
  });

  factory ChatSuggestionModel.fromJson(Map<String, dynamic> json) {
    final String rawMessage = json['message'] as String;
    List<String> allSuggestions = [];
    Map<String, dynamic>? aiTripData;
    Map<String, dynamic>? aiEventData;
    Map<String, dynamic>? aiListData;
    Map<String, dynamic>? aiTripIntentData;
    String processedMessage = '';

    // Handle JSON in code blocks
    if (rawMessage.contains("```json") && rawMessage.contains("```")) {
      final startIndex = rawMessage.indexOf("```json");
      final endIndex = rawMessage.lastIndexOf("```") + 3;
      final jsonBlock = rawMessage
          .substring(startIndex, endIndex)
          .replaceAll("```json", "")
          .replaceAll("```", "");

      try {
        final jsonMap = jsonDecode(jsonBlock);
        _extractSuggestions(jsonMap, allSuggestions);
        if (jsonMap.containsKey('ai_trip')) {
          aiTripData = jsonMap['ai_trip'];
        }
        if (jsonMap.containsKey('ai_event')) {
          aiEventData = jsonMap['ai_event'];
        }
        if (jsonMap.containsKey('ai_list')) {
          aiListData = jsonMap['ai_list'];
        }
        // Check for intent structure
        if (jsonMap.containsKey('trip_intent')) {
          aiTripIntentData = jsonMap;
        }
        processedMessage =
            rawMessage.replaceRange(startIndex, endIndex, "").trim();
      } catch (e) {
        processedMessage = rawMessage;
      }
    } else {
      // Process each line separately
      final lines = rawMessage.split('\n');
      List<String> processedLines = [];

      for (String line in lines) {
        String processedLine = line;

        // Find JSON objects in the line
        int startIndex = line.indexOf('{');
        while (startIndex != -1) {
          int endIndex = -1;
          int braceCount = 0;

          // Find the matching closing brace
          for (int i = startIndex; i < line.length; i++) {
            if (line[i] == '{') braceCount++;
            if (line[i] == '}') braceCount--;
            if (braceCount == 0) {
              endIndex = i + 1;
              break;
            }
          }

          if (endIndex != -1) {
            try {
              final jsonStr = line.substring(startIndex, endIndex);
              final jsonMap = jsonDecode(jsonStr);
              _extractSuggestions(jsonMap, allSuggestions);
              if (jsonMap.containsKey('ai_trip')) {
                aiTripData = jsonMap['ai_trip'];
              }
              if (jsonMap.containsKey('ai_event')) {
                aiEventData = jsonMap['ai_event'];
              }
              if (jsonMap.containsKey('ai_list')) {
                aiListData = jsonMap['ai_list'];
              }
              // Check for intent structure
              if (jsonMap.containsKey('trip_intent')) {
                aiTripIntentData = jsonMap;
              }
              processedLine = processedLine.replaceFirst(jsonStr, '').trim();
            } catch (e) {
              // If JSON parsing fails, keep the line as is
            }
          }

          startIndex = line.indexOf('{', startIndex + 1);
        }

        if (processedLine.isNotEmpty) {
          processedLines.add(processedLine);
        }
      }

      processedMessage = processedLines.join('\n').trim();
    }

    return ChatSuggestionModel(
      message: processedMessage,
      suggestions: allSuggestions,
      aiTrip: aiTripData,
      aiEvent: aiEventData,
      aiList: aiListData,
      aiTripIntent: aiTripIntentData,
    );
  }

  static void _extractSuggestions(dynamic jsonMap, List<String> suggestions) {
    if (jsonMap is Map<String, dynamic>) {
      jsonMap.forEach((key, value) {
        if (value is Map<String, dynamic>) {
          // Handle nested categories like "cities" and "interests"
          if (value.containsKey('suggestion')) {
            suggestions.addAll(List<String>.from(value['suggestion']));
          }
        } else if (key == 'suggestion' && value is List) {
          // Handle direct suggestion array
          suggestions.addAll(List<String>.from(value));
        }
      });
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'suggestions': suggestions,
      if (aiTrip != null) 'ai_trip': aiTrip,
      if (aiEvent != null) 'ai_event': aiEvent,
      if (aiList != null) 'ai_list': aiList,
      if (aiTripIntent != null) 'ai_trip_intent': aiTripIntent,
    };
  }
}
