import 'dart:convert';
import 'package:family_app/screen/main/chat/chat_context.dart';

class ChatSuggestionModel {
  final String message;
  final List<String> suggestions;
  final Map<String, dynamic>? aiTrip;
  final Map<String, dynamic>? aiEvent;
  final Map<String, dynamic>? aiList;
  final Map<String, dynamic>? aiTripIntent;

  ChatSuggestionModel({
    required this.message,
    required this.suggestions,
    this.aiTrip,
    this.aiEvent,
    this.aiList,
    this.aiTripIntent,
  });

  factory ChatSuggestionModel.fromJson(Map<String, dynamic> json,
      {AIPurposeKey? purposeKey}) {
    // Route to purpose-specific parsing if needed
    if (purposeKey != null) {
      return _parseWithPurpose(json, purposeKey);
    }

    // Default parsing logic (existing behavior)
    return _parseDefault(json);
  }

  /// Default parsing logic for backward compatibility
  static ChatSuggestionModel _parseDefault(Map<String, dynamic> json) {
    final String rawMessage = json['message'] as String;
    List<String> allSuggestions = [];
    Map<String, dynamic>? aiTripData;
    Map<String, dynamic>? aiEventData;
    Map<String, dynamic>? aiListData;
    Map<String, dynamic>? aiTripIntentData;
    String processedMessage = '';

    // Handle JSON in code blocks
    if (rawMessage.contains("```json") && rawMessage.contains("```")) {
      final startIndex = rawMessage.indexOf("```json");
      final endIndex = rawMessage.lastIndexOf("```") + 3;
      final jsonBlock = rawMessage
          .substring(startIndex, endIndex)
          .replaceAll("```json", "")
          .replaceAll("```", "");

      try {
        final jsonMap = jsonDecode(jsonBlock);
        _extractSuggestions(jsonMap, allSuggestions);
        if (jsonMap.containsKey('ai_trip')) {
          aiTripData = jsonMap['ai_trip'];
        }
        if (jsonMap.containsKey('ai_event')) {
          aiEventData = jsonMap['ai_event'];
        }
        if (jsonMap.containsKey('ai_list')) {
          aiListData = jsonMap['ai_list'];
        }
        // Check for intent structure
        if (jsonMap.containsKey('trip_intent')) {
          aiTripIntentData = jsonMap;
        }
        processedMessage =
            rawMessage.replaceRange(startIndex, endIndex, "").trim();
      } catch (e) {
        processedMessage = rawMessage;
      }
    } else {
      // Process each line separately
      final lines = rawMessage.split('\n');
      List<String> processedLines = [];

      for (String line in lines) {
        String processedLine = line;

        // Find JSON objects in the line
        int startIndex = line.indexOf('{');
        while (startIndex != -1) {
          int endIndex = -1;
          int braceCount = 0;

          // Find the matching closing brace
          for (int i = startIndex; i < line.length; i++) {
            if (line[i] == '{') braceCount++;
            if (line[i] == '}') braceCount--;
            if (braceCount == 0) {
              endIndex = i + 1;
              break;
            }
          }

          if (endIndex != -1) {
            try {
              final jsonStr = line.substring(startIndex, endIndex);
              final jsonMap = jsonDecode(jsonStr);
              _extractSuggestions(jsonMap, allSuggestions);
              if (jsonMap.containsKey('ai_trip')) {
                aiTripData = jsonMap['ai_trip'];
              }
              if (jsonMap.containsKey('ai_event')) {
                aiEventData = jsonMap['ai_event'];
              }
              if (jsonMap.containsKey('ai_list')) {
                aiListData = jsonMap['ai_list'];
              }
              // Check for intent structure
              if (jsonMap.containsKey('trip_intent')) {
                aiTripIntentData = jsonMap;
              }
              processedLine = processedLine.replaceFirst(jsonStr, '').trim();
            } catch (e) {
              // If JSON parsing fails, keep the line as is
            }
          }

          startIndex = line.indexOf('{', startIndex + 1);
        }

        if (processedLine.isNotEmpty) {
          processedLines.add(processedLine);
        }
      }

      processedMessage = processedLines.join('\n').trim();
    }

    return ChatSuggestionModel(
      message: processedMessage,
      suggestions: allSuggestions,
      aiTrip: aiTripData,
      aiEvent: aiEventData,
      aiList: aiListData,
      aiTripIntent: aiTripIntentData,
    );
  }

  /// Purpose-specific parsing logic
  static ChatSuggestionModel _parseWithPurpose(
      Map<String, dynamic> json, AIPurposeKey purposeKey) {
    switch (purposeKey) {
      case AIPurposeKey.editATrip:
        return _parseEditTripResponse(json);
      case AIPurposeKey.planATrip:
        return _parseCreateTripResponse(json);
      case AIPurposeKey.planAnEvent:
      case AIPurposeKey.editAnEvent:
        return _parseEventResponse(json);
      case AIPurposeKey.createAList:
      case AIPurposeKey.editAList:
        return _parseListResponse(json);
      case AIPurposeKey.generalPurpose:
        return _parseDefault(json);
    }
  }

  /// Parse Edit Trip responses - handles JSON Patch format and trip deltas
  static ChatSuggestionModel _parseEditTripResponse(Map<String, dynamic> json) {
    // For edit trip, we might receive different JSON structures:
    // 1. JSON Patch format (RFC 6902) for incremental updates
    // 2. Full trip object with modifications
    // 3. Confirmation messages with suggestions

    final String rawMessage = json['message'] as String;
    List<String> allSuggestions = [];
    Map<String, dynamic>? aiTripData;
    String processedMessage = '';

    // Handle JSON in code blocks or inline
    if (rawMessage.contains("```json") && rawMessage.contains("```")) {
      final startIndex = rawMessage.indexOf("```json");
      final endIndex = rawMessage.lastIndexOf("```") + 3;
      final jsonBlock = rawMessage
          .substring(startIndex, endIndex)
          .replaceAll("```json", "")
          .replaceAll("```", "");

      try {
        final jsonMap = jsonDecode(jsonBlock);
        _extractSuggestions(jsonMap, allSuggestions);

        // Check for edit trip specific structures
        if (jsonMap.containsKey('ai_trip')) {
          aiTripData = jsonMap['ai_trip'];
        } else if (jsonMap.containsKey('trip_delta')) {
          // Handle JSON Patch format for trip modifications
          aiTripData = jsonMap;
        } else if (jsonMap.containsKey('modifications')) {
          // Handle modification confirmations
          aiTripData = jsonMap;
        }

        processedMessage =
            rawMessage.replaceRange(startIndex, endIndex, "").trim();
      } catch (e) {
        processedMessage = rawMessage;
      }
    } else {
      // Process inline JSON for edit trip responses
      final result = _processInlineJsonForEditTrip(rawMessage, allSuggestions);
      processedMessage = result['message'];
      if (result['tripData'] != null) {
        aiTripData = result['tripData'];
      }
    }

    return ChatSuggestionModel(
      message: processedMessage,
      suggestions: allSuggestions,
      aiTrip: aiTripData,
      aiEvent: null,
      aiList: null,
      aiTripIntent: null,
    );
  }

  /// Process inline JSON specifically for edit trip responses
  static Map<String, dynamic> _processInlineJsonForEditTrip(
      String rawMessage, List<String> suggestions) {
    final lines = rawMessage.split('\n');
    List<String> processedLines = [];
    Map<String, dynamic>? tripData;

    for (String line in lines) {
      String processedLine = line;
      int startIndex = line.indexOf('{');

      while (startIndex != -1) {
        int endIndex = -1;
        int braceCount = 0;

        for (int i = startIndex; i < line.length; i++) {
          if (line[i] == '{') braceCount++;
          if (line[i] == '}') braceCount--;
          if (braceCount == 0) {
            endIndex = i + 1;
            break;
          }
        }

        if (endIndex != -1) {
          try {
            final jsonStr = line.substring(startIndex, endIndex);
            final jsonMap = jsonDecode(jsonStr);
            _extractSuggestions(jsonMap, suggestions);

            // Handle edit trip specific JSON structures
            if (jsonMap.containsKey('ai_trip') ||
                jsonMap.containsKey('trip_delta') ||
                jsonMap.containsKey('modifications')) {
              tripData = jsonMap;
            }

            processedLine = processedLine.replaceFirst(jsonStr, '').trim();
          } catch (e) {
            // If JSON parsing fails, keep the line as is
          }
        }

        startIndex = line.indexOf('{', startIndex + 1);
      }

      if (processedLine.isNotEmpty) {
        processedLines.add(processedLine);
      }
    }

    return {
      'message': processedLines.join('\n').trim(),
      'tripData': tripData,
    };
  }

  /// Parse Create Trip responses - standard trip creation
  static ChatSuggestionModel _parseCreateTripResponse(
      Map<String, dynamic> json) {
    // For create trip, use the default parsing logic but with trip-specific focus
    return _parseDefault(json);
  }

  /// Parse Event responses - handles event creation and editing
  static ChatSuggestionModel _parseEventResponse(Map<String, dynamic> json) {
    // For events, use the default parsing logic but with event-specific focus
    return _parseDefault(json);
  }

  /// Parse List responses - handles list creation and editing
  static ChatSuggestionModel _parseListResponse(Map<String, dynamic> json) {
    // For lists, use the default parsing logic but with list-specific focus
    return _parseDefault(json);
  }

  static void _extractSuggestions(dynamic jsonMap, List<String> suggestions) {
    if (jsonMap is Map<String, dynamic>) {
      jsonMap.forEach((key, value) {
        if (value is Map<String, dynamic>) {
          // Handle nested categories like "cities" and "interests"
          if (value.containsKey('suggestion')) {
            suggestions.addAll(List<String>.from(value['suggestion']));
          }
        } else if (key == 'suggestion' && value is List) {
          // Handle direct suggestion array
          suggestions.addAll(List<String>.from(value));
        }
      });
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'suggestions': suggestions,
      if (aiTrip != null) 'ai_trip': aiTrip,
      if (aiEvent != null) 'ai_event': aiEvent,
      if (aiList != null) 'ai_list': aiList,
      if (aiTripIntent != null) 'ai_trip_intent': aiTripIntent,
    };
  }
}
