import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/ai_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/usecase/model/event_parameter.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/screen/main/chat/chat_state.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/main_cubit.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_parameter.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/data/usecase/event_usecase.dart';
import 'package:family_app/data/model/chat_suggestion_model.dart';
import 'package:family_app/main.dart';
import 'package:auto_route/auto_route.dart';
import 'package:family_app/router/app_route.dart';

class ChatCubit extends BaseCubit<ChatState> {
  // final MainCubit mainCubit;
  final IActivityRepository activityRepository;
  final ChatParameter parameter;
  late WebSocketChannel _channel;
  Timer? _timeoutTimer;

  final EventUsecase eventUsecase = locator.get();
  final AccountService accountService = locator.get();
  final HomeCubit homeCubit;

  ChatCubit({
    required this.activityRepository,
    required this.parameter,
    required this.homeCubit,
  }) : super(ChatState());
  FlutterTts flutterTts = FlutterTts();

  @override
  Future<void> onInit() async {
    super.onInit();
    AppLogger.d("chat parameter uuid: ${parameter.chatContext.tripId}");
    await _initializeWebSocketChannel();
    _startTimeout();
    _initTTS();
    isSpeechResultFinalAndStopListening.addListener(sendSpeechMessage);
    // fetchActivity();
  }

  Future<void> _initTTS() async {
    await flutterTts.setLanguage("en-US");
    await flutterTts.setSpeechRate(0.5);
    await flutterTts.setVolume(1.0);
    await flutterTts.setPitch(1.0);
    await flutterTts.awaitSpeakCompletion(true);

    var voices = await flutterTts.getVoices;
    if (voices == null) {
      AppLogger.d("No voices available");
      return;
    }
    if (Platform.isIOS) {
      // filter the voices en-US
      await flutterTts.setVoice({
        "identifier": "com.apple.ttsbundle.siri_Nicky_en-US_compact",
        "locale": "en-US",
        "name": "Nicky",
        "quality": "default",
        "gender": "female"
      });
    }
    if (Platform.isAndroid) {
      //female voice
      await flutterTts.setVoice({
        'name': 'en-us-x-sfg#female_1-local', // Change to the voice you found
        'locale': 'en-US'
      });
    }
    log("TTS Initialized! $voices");
  }

  @override
  Future<void> close() async {
    AppLogger.d("re-assemble");
    _closeWebSocketChannel();
    _timer?.cancel();
    await _audioRecorder.dispose();
    await flutterTts.stop();
    await speechToText.stop();
    isSpeechResultFinalAndStopListening.removeListener(sendSpeechMessage);
    isSpeechResultFinalAndStopListening.dispose();
    super.close();
  }

  Future<void> _initializeWebSocketChannel() async {
    AppLogger.d("initializeWebSocketChannel");
    final token = parameter.chatContext.token;

    try {
      _channel = WebSocketChannel.connect(
        Uri.parse('wss://vrelay-vn1.5gencare.com/ai/ws?authorization=$token'),
      );
      _channel.stream.listen(
        _handleStreamData,
        onError: _handleStreamError,
        onDone: _handleStreamDone,
      );

      // Wait for the connection to be established
      await _channel.ready;

      Map<String, String> init_msg =
          await parameter.chatContext.getInitMessage();
      final jsonMessage = jsonEncode(init_msg);
      AppLogger.d("Sending INIT message: $jsonMessage");
      _channel.sink.add(jsonMessage);

      emit(state.copyWith(connState: ConnState.success, loading: false));
    } catch (e) {
      AppLogger.e("WebSocket connection error: $e");
      emit(state.copyWith(connState: ConnState.none, loading: false));
      _handleStreamError(e);
    }
  }

  void _closeWebSocketChannel() {
    _timeoutTimer?.cancel();
    if (_channel != null) {
      _channel.sink.close();
      AppLogger.d("WebSocket connection closed");
    }
  }

  void _startTimeout() {
    // logd("Starting timeout");
    _timeoutTimer = Timer(Duration(seconds: 60), _handleTimeout);
  }

  void _handleTimeout() {
    _timeoutTimer?.cancel();
    _handleStreamError('Connection timeout');
    if (state.isRecording) {
      _timer?.cancel();
      _audioRecorder.cancel().then((_) {
        emit(state.clearRecordingInfo().copyWith(
            connState: ConnState.timeout,
            loading: false,
            isWaitingForResponse: false));
      });
      cancelListening();
    } else {
      emit(state.copyWith(
          connState: ConnState.timeout,
          loading: false,
          messages: [],
          isWaitingForResponse: false));
    }
  }

  void _handleStreamData(dynamic data) async {
    final response = jsonDecode(data) as Map<String, dynamic>;

    logd("_handleStreamData state: ${state.loading} , response: $response");
    final List<Map<String, dynamic>> allMessages = List.from(state.messages);
    _timeoutTimer?.cancel();

    Trip? aiTrip;
    FlEvent? aiEvent;
    FlList? aiList;
    Map<String, dynamic>? aiTripIntent;
    dynamic newMessage;
    ConnState connState = ConnState.none;
    if (response['command'] == "ai_chat_repond") {
      //dont add the empty message to the list
      if (response['message'] != "") {
        connState = ConnState.success;
        // Use purpose-aware parsing for different chat contexts
        final suggestionModel = ChatSuggestionModel.fromJson(response,
            purposeKey: parameter.chatContext.purposeKey);

        // Process AI response using simplified logic
        final responseData = await _processAIResponse(suggestionModel);
        aiTrip = responseData.aiTrip;
        aiEvent = responseData.aiEvent;
        aiList = responseData.aiList;
        aiTripIntent = responseData.aiTripIntent;
        newMessage = responseData.message;
      } // if response message != null
    } else {
      newMessage = {
        "type": "unknown",
        "message": 'Unknown response: $response',
        "timestamp": DateFormat('HH:mm').format(DateTime.now())
      };
      connState = ConnState.success;
    }

    var latestMess;
    if (allMessages.isNotEmpty) {
      latestMess = allMessages.last;
    }
    log("latestMess: $latestMess");
    log("newMessage: $newMessage");
    if (latestMess != null &&
        (latestMess['message_type'] == MessageType.audio ||
            latestMess['auto_speak']) &&
        newMessage['type'] == 'response') {
      // remove the last message
      var messageString = newMessage['message'];
      flutterTts.speak(messageString);
    }

    allMessages.add(newMessage);

    emit(state.copyWith(
        messages: allMessages,
        connState: connState,
        loading: false,
        isWaitingForResponse: false,
        aiTrip: aiTrip,
        aiEvent: aiEvent,
        aiList: aiList,
        aiTripIntent: aiTripIntent));
  }

  /// Process AI response based on the parsed suggestion model
  Future<_ResponseData> _processAIResponse(
      ChatSuggestionModel suggestionModel) async {
    Trip? aiTrip;
    FlEvent? aiEvent;
    FlList? aiList;
    Map<String, dynamic>? aiTripIntent;
    Map<String, dynamic> newMessage;

    // Extract data from suggestion model
    dynamic trip = suggestionModel.aiTrip;
    dynamic jsonAiEvent = suggestionModel.aiEvent;
    dynamic jsonAiList = suggestionModel.aiList;
    dynamic jsonAiTripIntent = suggestionModel.aiTripIntent;

    if (trip != null) {
      // Handle trip response
      aiTrip = Trip.fromJson(trip);
      aiTrip.familyId = accountService.familyId;
      AppLogger.d(
          "chatContext purposeKey: ${parameter.chatContext.purposeKey}, tripId: ${parameter.chatContext.tripId}");

      // For edit trip, preserve the existing trip ID
      if (parameter.chatContext.purposeKey == AIPurposeKey.editATrip) {
        aiTrip.uuid = parameter.chatContext.tripId!;
      }

      newMessage = {
        "trip_available": true,
        "type": "response",
        "message": suggestionModel.message,
        "timestamp": DateFormat('HH:mm').format(DateTime.now()),
        "suggestions": suggestionModel.suggestions
      };
    } else if (jsonAiEvent != null) {
      // Handle event response
      aiEvent = FlEvent.fromJson(jsonAiEvent);
      logd("Creating event: $aiEvent");

      // Create the new event in user calendar
      var newEvent = await createEvent(aiEvent);

      newMessage = {
        "event_available": true,
        "type": "response",
        "message": suggestionModel.message,
        "timestamp": DateFormat('HH:mm').format(DateTime.now()),
        "suggestions": suggestionModel.suggestions,
        "newEvent": newEvent
      };
    } else if (jsonAiList != null) {
      // Handle list response
      aiList = FlList.fromJson(jsonAiList);
      logd("Creating list: $aiList");

      newMessage = {
        "list_available": true,
        "type": "response",
        "message": suggestionModel.message,
        "timestamp": DateFormat('HH:mm').format(DateTime.now()),
        "suggestions": suggestionModel.suggestions
      };
    } else if (jsonAiTripIntent != null) {
      // Handle trip intent - navigate to VTP
      logd("Trip intent detected: $jsonAiTripIntent");
      aiTripIntent = jsonAiTripIntent;

      newMessage = {
        "trip_intent_available": true,
        "type": "response",
        "message": suggestionModel.message,
        "timestamp": DateFormat('HH:mm').format(DateTime.now()),
        "suggestions": suggestionModel.suggestions
      };
    } else {
      // Handle general response
      newMessage = {
        "type": "response",
        "message": suggestionModel.message,
        "timestamp": DateFormat('HH:mm').format(DateTime.now()),
        "suggestions": suggestionModel.suggestions
      };
    }

    return _ResponseData(
      aiTrip: aiTrip,
      aiEvent: aiEvent,
      aiList: aiList,
      aiTripIntent: aiTripIntent,
      message: newMessage,
    );
  }

  void _handleStreamError(dynamic error) {
    // Cancel the timeout timer when there's an error
    _timeoutTimer?.cancel();

    AppLogger.e("error: $error");
    final newMessage = {
      "type": "error",
      "message": 'Error: $error',
      "timestamp": DateFormat('HH:mm').format(DateTime.now())
    };
    // final messages = state.messages;
    final List<Map<String, dynamic>> messages = List.from(state.messages);
    messages.add(newMessage);
    emit(state.copyWith(
        connState: ConnState.error,
        messages: messages,
        isRecording: false,
        isWaitingForResponse: false));

    // Cancel any ongoing recording
    if (state.isRecording) {
      _timer?.cancel();
      _audioRecorder.cancel();
      cancelListening();
    }
  }

  void _handleStreamDone() {
    // Cancel the timeout timer when the stream is done
    _timeoutTimer?.cancel();

    AppLogger.d("WebSocket connection closed , stream DONE?");
    emit(state.copyWith(
        connState: ConnState.done,
        isWaitingForResponse: false,
        loading: false));
  }

  Future<void> pickImage() async {
    final result = await FilePicker.platform.pickFiles(type: FileType.image);

    if (result == null || result.files.isEmpty) {
      throw Exception('No files picked or file picker was canceled');
    }

    AppLogger.d("Image selected: ${result.files.single.name}");
    AppLogger.d("Image size: ${result.files.single.size}");
    String base64ImageStr = base64Encode(result.files.single.bytes!);
    String fileNameStr = result.files.single.name;
    AppLogger.d("Image selected: $fileNameStr");
    emit(state.copyWith(base64Image: base64ImageStr, fileName: fileNameStr));
  }

  // }

  Future<void> reconnect() async {
    try {
      emit(state.copyWith(loading: true, messages: []));
      _closeWebSocketChannel();
      await _initializeWebSocketChannel();
    } catch (e) {
      AppLogger.e("Reconnection failed: $e");
      emit(state.copyWith(connState: ConnState.none, loading: false));
    }
  }

  void resetConnectionState() {
    emit(state.copyWith(connState: ConnState.none));
  }

  Future<void> sendMessage(
      String message, String? base64Image, String? fileName,
      {bool? autoSpeak}) async {
    // Cancel existing timer if any
    _timeoutTimer?.cancel();

    final List<Map<String, dynamic>> allMessages = List.from(state.messages);
    final newMessage = {
      'message': message,
      'type': 'user',
      'message_type': MessageType.text,
      'auto_speak': autoSpeak ?? false,
      'timestamp': DateFormat('HH:mm').format(DateTime.now()),
    };
    allMessages.add(newMessage);
    emit(state.copyWith(
        messages: allMessages,
        connState: ConnState.success,
        isWaitingForResponse: true,
        base64Image: base64Image,
        fileName: fileName));
    final jsonMessage = jsonEncode({
      "command": "ai_chat",
      "message": message,
      "auto_create": ChatContext.autoCreateFalse,
      "file": base64Image,
      "file_name": fileName,
      "prompt": parameter.chatContext.aiPrompt,
    });

    if (base64Image != null) {
      AppLogger.d("Sending message with attached file name: $fileName");
    } else {
      AppLogger.d("Sending message: $jsonMessage");
    }
    _channel.sink.add(jsonMessage);
    // Start new timeout timer
    _startTimeout();
  }

  // Test method to trigger VisualTripPlannerRoute
  void testVisualTripPlanner() {
    // Don't close the chat channel - let it stay connected
    // The chat screen will handle reconnection when returning from VTP

    final context = navigatorKey.currentContext;
    if (context != null) {
      final sampleData = {
        'trip_intent': 'Direct', //'Discovery',
        'destination': 'New York'
      };

      context.router.push(
        VisualTripPlannerRoute(
          parameter: VisualTripPlannerParameter(
            initialData: sampleData,
          ),
        ),
      );
    }
  }

  Future<void> sendAudioMessage(String audioFilePath) async {
    // Cancel existing timer if any
    _timeoutTimer?.cancel();

    final List<Map<String, dynamic>> allMessages = List.from(state.messages);
    final newMessage = {
      "message": '',
      'type': 'user',
      'message_type': MessageType.audio,
      'timestamp': DateFormat('HH:mm').format(DateTime.now()),
    };
    allMessages.add(newMessage);
    // Convert the audio file to base64
    final audioFile = File(audioFilePath);
    final fileName = audioFile.path.split('/').last;
    final base64Audio = base64Encode(audioFile.readAsBytesSync());
    emit(state.copyWith(
        messages: allMessages,
        connState: ConnState.success,
        isWaitingForResponse: true));
    final jsonMessage = jsonEncode({
      "command": "ai_chat",
      "message": "",
      "auto_create": ChatContext.autoCreateFalse,
      "file": base64Audio,
      "file_name": fileName,
    });

    AppLogger.d("Sending audio message");
    _channel.sink.add(jsonMessage);

    // Start new timeout timer
    _startTimeout();
  }

  final AudioRecorder _audioRecorder = AudioRecorder();
  Timer? _timer;
  Future<void> startRecording() async {
    try {
      PermissionStatus status = await Permission.microphone.status;
      if (status.isDenied) {
        status = await Permission.microphone.request();
        emit(state.copyWith(
            isRecording: false, errorMessage: 'Permission denied'));
        return;
      }

      if (await _audioRecorder.hasPermission()) {
        final dir = await getTemporaryDirectory();
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final fileName = 'voice_recording_$timestamp.wav';
        final filePath = '${dir.path}/$fileName';
        await _audioRecorder.start(
          const RecordConfig(
            encoder: AudioEncoder.wav,
            sampleRate: 44100,
          ),
          path: filePath,
        );

        // Update progress every second
        _timer?.cancel();
        _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
          emit(state.copyWith(
              recordingDuration:
                  state.recordingDuration + const Duration(seconds: 1)));
        });

        emit(state.copyWith(isRecording: true));
      } else {
        emit(state.copyWith(
            isRecording: false, errorMessage: 'Permission denied'));
      }
    } catch (e) {
      AppLogger.e('Error starting recording: $e');
      emit(state.copyWith(
          isRecording: false, errorMessage: 'Failed to record audio'));
    }
  }

  Future<void> stopRecording() async {
    try {
      _timer?.cancel();
      final path = await _audioRecorder.stop();
      AppLogger.d('Recording saved at: $path');
      if (path != null) {
        sendAudioMessage(path);
        emit(state.clearRecordingInfo());
      } else {
        emit(state.copyWith(
            isRecording: false, errorMessage: 'Failed to save audio'));
      }
    } catch (e) {
      AppLogger.e('Error stopping recording: $e');
      emit(state.copyWith(
          isRecording: false, errorMessage: 'Failed to save audio'));
    }
  }

  Future<void> cancelRecording() async {
    try {
      _timer?.cancel();
      await _audioRecorder.cancel();
      AppLogger.d("Recording cancelled");
      emit(state.clearRecordingInfo());
    } catch (e) {
      AppLogger.e('Error stopping recording: $e');
      emit(state.clearRecordingInfo());
    }
  }

  void updateSlideToCancelOffset(Offset offset) {
    emit(state.copyWith(slideToCancelOffset: offset));
  }

  Future<EventModels> createEvent(FlEvent aiEvent) async {
    final FlEvent event = aiEvent;
    //final eventModel = event.toEventModel();

    //*** HERE ALL TIME SHOULD BE IN UTC Already */

    DateTime startDate = event.fromDate.toDateTime();
    DateTime endDate = event.toDate.toDateTime();
    DateTime? reminderTime;

    //     if(state.isAllDay) {
    //       startDate = DateTime(startDate.year, startDate.month, startDate.day);
    //       endDate = DateTime(endDate.year, endDate.month, endDate.day);
    //     }

    //just set reminder to 30min
    var defaultReminder = locator.get<LocalStorage>().defaultReminder;
    if (defaultReminder > 0) {
      reminderTime = startDate.subtract(Duration(minutes: defaultReminder));
    }

    final result = await eventUsecase.call(
      EventParameter(
        id: '',
        //sure it's empty
        name: event.name.trim(),
        fromDate: startDate.toIso8601String(),
        toDate: endDate.toIso8601String(),
        color: '',
        caption: '',
        description: event.description.trim(),
        familyId: accountService.familyId,
        activityId: '',
        notificationStatus: '',
        notificationTime: reminderTime?.toIso8601String() ?? '',
        // repeatType: EventRepeatType.none,
        members: [],
      ),
    );

    logd("Event created successfully");
    logd("Event is: $result");
    homeCubit.onRefresh();
    return result;
  }

  // Add this public method for testing
  @visibleForTesting
  void handleStreamDataForTesting(dynamic data) {
    _handleStreamData(data);
  }

  final SpeechToText speechToText = SpeechToText();
  String currentLocaleId = '';
  String? latestTranscribedText;
  late bool isSpeechResultFinal = false;
  ValueNotifier<bool> isSpeechResultFinalAndStopListening =
      ValueNotifier(false);

  Future<void> sendSpeechMessage() async {
    if (isSpeechResultFinalAndStopListening.value) {
      if (latestTranscribedText != null && latestTranscribedText!.isNotEmpty) {
        await sendMessage(latestTranscribedText!, null, null, autoSpeak: true);
        latestTranscribedText = null;
        isSpeechResultFinalAndStopListening.value = false;
      } else {
        emit(state.copyWith(errorMessage: 'No speech detected'));
      }
    }
  }

  Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.status;

    if (status.isDenied) {
      final result = await Permission.microphone.request();
      if (!result.isGranted) {
        emit(state.copyWith(
          isRecording: false,
          errorMessage: 'Microphone permission denied',
        ));
        return false;
      }
    } else if (status.isRestricted || status.isPermanentlyDenied) {
      emit(state.copyWith(
        isRecording: false,
        errorMessage: 'Microphone permission required',
      ));
      return false;
    }

    return true;
  }

  Future<void> startListening() async {
    try {
      // Check availability first
      var hasSpeech = await speechToText.initialize();
      if (hasSpeech) {
        var systemLocale = await speechToText.systemLocale();
        currentLocaleId = systemLocale?.localeId ?? 'en_US';
        // currentLocaleId = 'en_US';
      } else {
        emit(state.copyWith(
          isRecording: false,
          errorMessage: 'Speech recognition unavailable',
        ));
        return;
      }
      // Request permissions
      final hasPermission = await requestMicrophonePermission();
      if (hasPermission) {
        // Start listening
        final options = SpeechListenOptions(
            partialResults: false,
            cancelOnError: true,
            autoPunctuation: true,
            enableHapticFeedback: true);
        speechToText.listen(
            onResult: onSpeechResult,
            localeId: currentLocaleId,
            listenOptions: options);

        // Update progress every second
        _timer?.cancel();
        _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
          emit(state.copyWith(
              recordingDuration:
                  state.recordingDuration + const Duration(seconds: 1)));
        });
        await flutterTts.stop(); // Stop TTS if it's speaking
        isSpeechResultFinal = false;
        isSpeechResultFinalAndStopListening.value = false;
        latestTranscribedText = null;
        emit(state.copyWith(isRecording: true));
      } else {
        emit(state.copyWith(
            isRecording: false, errorMessage: 'Permission denied'));
      }
    } catch (e) {
      AppLogger.e("Speech recognition error: $e");
      emit(state.copyWith(
        isRecording: false,
        errorMessage: "Speech recognition error: $e",
      ));
    }
  }

  Future<void> onSpeechResult(SpeechRecognitionResult result) async {
    AppLogger.d(
        'Speech recognition: Partial result - "${result.recognizedWords}"');
    isSpeechResultFinal = result.finalResult;
    if (isSpeechResultFinal) {
      AppLogger.d(
          'Speech recognition: Final result confirmed - "${result.recognizedWords}"');
      latestTranscribedText = result.recognizedWords;
    }
    isSpeechResultFinalAndStopListening.value =
        isSpeechResultFinal && !state.isRecording;
  }

  Future<void> cancelListening() async {
    try {
      _timer?.cancel();
      await speechToText.cancel();
    } catch (e) {
      AppLogger.e('Error cancel speech recognition: $e');
    } finally {
      emit(state.clearRecordingInfo());
    }
  }

  Future<void> stopListening() async {
    try {
      _timer?.cancel();
      await speechToText.stop();
      isSpeechResultFinalAndStopListening.value = isSpeechResultFinal;
      emit(state.clearRecordingInfo());
    } catch (e) {
      AppLogger.e('Error stopping speech recognition: $e');
      emit(state.copyWith(
          isRecording: false,
          errorMessage: 'Failed to stop speech recognition'));
    }
  }
}

/// Helper class to organize response data from AI processing
class _ResponseData {
  final Trip? aiTrip;
  final FlEvent? aiEvent;
  final FlList? aiList;
  final Map<String, dynamic>? aiTripIntent;
  final Map<String, dynamic> message;

  _ResponseData({
    this.aiTrip,
    this.aiEvent,
    this.aiList,
    this.aiTripIntent,
    required this.message,
  });
}
